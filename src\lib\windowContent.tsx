import React from 'react';
import RecycleBinContent from '../components/windows/RecycleBinContent';
import SkillsContent from '../components/windows/SkillsContent';
import MyComputerContent from '../components/windows/MyComputerContent';
import MyDocumentsContent from '../components/windows/MyDocumentsContent';
import MyPicturesContent from '../components/windows/MyPicturesContent';
import MyMusicContent from '../components/windows/MyMusicContent';
import ControlPanelContent from '../components/windows/ControlPanelContent';
import ResumeContent from '../components/windows/ResumeContent';
import ContactContent from '../components/windows/ContactContent';
import ProjectFolder from '../components/ProjectFolder';
import ProjectViewer from '../components/windows/ProjectViewer';

type OpenWindowFunc = (id: string, name: string) => void;
type UpdateWindowSizeFunc = (windowId: string, size: { width: number; height: number }) => void;

interface WindowContentOptions {
  onOpenWindow: OpenWindowFunc;
  updateWindowSize?: UpdateWindowSizeFunc;
  windowId?: string;
}

export const getWindowContent = (iconId: string, options: OpenWindowFunc | WindowContentOptions): React.ReactNode => {
  // Handle backward compatibility - if options is a function, it's the old onOpenWindow parameter
  const onOpenWindow = typeof options === 'function' ? options : options.onOpenWindow;
  const updateWindowSize = typeof options === 'object' ? options.updateWindowSize : undefined;
  const windowId = typeof options === 'object' ? options.windowId : undefined;
  switch (iconId) {
    case 'recyclebin':
      return <RecycleBinContent />;
    case 'my_computer':
      return <MyComputerContent />;
    case 'skills':
      return <SkillsContent />;
    case 'my_documents':
      return <MyDocumentsContent onOpenWindow={onOpenWindow} />;
    case 'my_pictures':
      return <MyPicturesContent />;
    case 'my_music':
      return <MyMusicContent />;
    case 'control_panel':
      return <ControlPanelContent />;
    case 'certifications':
      return (
        <div className="p-4 bg-white font-tahoma">
          <div className="flex items-center mb-4 pb-2 border-b-2 border-blue-500">
            <span className="text-2xl mr-2">🏆</span>
            <h2 className="text-lg font-bold text-blue-800">Professional Certifications</h2>
          </div>

          <div className="space-y-4">
            {/* Udemy Certificate */}
            <div className="bg-gray-50 p-4 rounded border hover:bg-blue-50 transition-colors">
              <div className="flex items-start space-x-3">
                <div className="w-12 h-12 bg-purple-500 rounded flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xl">📚</span>
                </div>
                <div className="flex-grow">
                  <h3 className="font-bold text-blue-900 mb-1">Udemy Certificate</h3>
                  <p className="text-sm text-gray-600 mb-2">SQL for Data Analysis: Advanced SQL Querying Techniques</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-700">
                    <div><strong>Issue Date:</strong> January 2025</div>
                    <div><strong>Credential ID:</strong> UC-4119aa4c-b6d3-452a-9a61-5f8c8b65f1b7</div>
                  </div>
                  <a
                    href="https://www.udemy.com/certificate/UC-4119aa4c-b6d3-452a-9a61-5f8c8b65f1b7/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block mt-2 text-sm text-blue-600 hover:underline"
                  >
                    View Certificate →
                  </a>
                </div>
              </div>
            </div>

            {/* CodeSignal Certificate */}
            <div className="bg-gray-50 p-4 rounded border hover:bg-blue-50 transition-colors">
              <div className="flex items-start space-x-3">
                <div className="w-12 h-12 bg-green-500 rounded flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xl">💻</span>
                </div>
                <div className="flex-grow">
                  <h3 className="font-bold text-blue-900 mb-1">CodeSignal Certificate</h3>
                  <p className="text-sm text-gray-600 mb-2">Mastering Algorithms and Data Structures in C#</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-700">
                    <div><strong>Issue Date:</strong> January 2025</div>
                    <div><strong>Platform:</strong> CodeSignal</div>
                  </div>
                  <a
                    href="https://codesignal.com/learn/certificates/cm4954vlx007fds9n0x4tg6o2/course-paths/108"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block mt-2 text-sm text-blue-600 hover:underline"
                  >
                    View Certificate →
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded border-l-4 border-blue-500">
            <p className="text-sm text-blue-800">
              <strong>Continuous Learning:</strong> I actively pursue professional development through
              online courses and certifications to stay current with industry trends and best practices.
            </p>
          </div>
        </div>
      );
    case 'projects':
      return (
        <div className="p-4 sm:p-6 bg-gradient-to-br from-gray-50 to-blue-50 min-h-full">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-5xl mx-auto justify-items-center">
            <ProjectFolder
              name="Zendo"
              description="Browser Extension"
              icon="🧘"
              onClick={() => onOpenWindow('project-zendo', 'Zendo - Browser Extension')}
            />
            <ProjectFolder
              name="Chromepanion"
              description="Browser Extension"
              icon="🤖"
              onClick={() => onOpenWindow('project-chromepanion', 'Chromepanion - Browser Extension')}
            />
            <ProjectFolder
              name="Mobile App"
              description="Rental Mobile App"
              icon="🚚"
              onClick={() => onOpenWindow('project-moverzz', 'a work in progress web and mobile app (android and ios) - for real estate')}
            />
            {/* <ProjectFolder
              name="E-Commerce Platform"
              description="Full-stack Web App"
              icon="🛍️"
              onClick={() => onOpenWindow('project-ecommerce', 'E-Commerce Platform')}
            /> */}
          </div>
        </div>
      );
    case 'my_resume':
      return <ResumeContent updateWindowSize={updateWindowSize} windowId={windowId} />;
    case 'contact':
      return <ContactContent />;
    case 'about':
      return (
        <div className="p-4 bg-white font-mono text-sm">
          <div className="whitespace-pre-line">
            {`Hello! I'm Mark Jovet Verano (markoverano), a passionate software engineer with over a decade of experience building applications and scalable systems.

I specialize in creating robust, user-focused software solutions that bridge the gap between complex technical requirements and intuitive user experiences. My journey in software engineering has been driven by a commitment to excellence and continuous learning.

After 12 years of coding behind the scenes, I finally got my act together and put up this portfolio site! Side projects that used to hide in the depths of my GitHub are now out in the open. Am I late to the portfolio party?

What I Do:
- Full-stack development with .NET, Angular and React
- Cloud architecture and DevOps implementation
- Mobile app development for iOS and Android
- System design and performance optimization
- Database design and management

My Professional Approach:
I believe in writing clean, maintainable code and creating solutions that truly serve users' needs. I'm always excited to learn new technologies and tackle challenging problems that push the boundaries of what's possible.

Current Technical Focus:
- Microservices architecture and distributed systems
- AI/ML integration in web applications
- Performance optimization and scalability
- Team leadership and technical mentoring
- Modern web technologies and frameworks
- Mobile app development

When I'm not coding, you can find me:
- Working on innovative side projects (wait, that's still coding!)
- Reading about emerging technologies and industry trends
- Hiking, swimming, camping, and enduro riding
- Contributing to open-source projects

Thanks for visiting my Windows XP themed portfolio. This nostalgic interface showcases my technical skills while paying homage to the golden age of computing. The design is fully responsive and mobile-friendly, demonstrating my ability to blend creativity with functionality.

Feel free to explore my projects and get in touch if you'd like to collaborate with markoverano!

---
Portfolio by Mark Jovet Verano (markoverano)
Last modified: 2025
File size: 1.8 KB`}
          </div>
        </div>
      );
    case 'project-zendo':
      return <ProjectViewer projectId="zendo" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    case 'project-chromepanion':
      return <ProjectViewer projectId="chromepanion" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    case 'project-moverzz':
      return <ProjectViewer projectId="moverzz" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    case 'project-ecommerce':
      return <ProjectViewer projectId="ecommerce" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    default:
      return <div className="p-4">Window content not found.</div>;
  }
};
