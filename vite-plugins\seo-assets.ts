import { Plugin } from 'vite';
import fs from 'fs';
import path from 'path';

export function seoAssetsPlugin(): Plugin {
  return {
    name: 'seo-assets',
    generateBundle(options, bundle) {
      const seoAssets = [
        'robots.txt',
        'sitemap.xml',
        'favicon.ico',
        'windows-logo.png'
      ];

      seoAssets.forEach(asset => {
        const assetPath = path.resolve('public', asset);
        if (fs.existsSync(assetPath)) {
          const content = fs.readFileSync(assetPath);
          this.emitFile({
            type: 'asset',
            fileName: asset,
            source: content
          });
        }
      });
    },
    transformIndexHtml: {
      enforce: 'post',
      transform(html, context) {
        if (context.bundle) {
          const preloadHints = `
    <!-- Preload critical resources for better SEO performance -->
    <link rel="preload" href="/windows-logo.png" as="image" type="image/png">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">`;
          
          html = html.replace('</head>', `${preloadHints}\n  </head>`);
          
          html = html.replace('<html', '<html lang="en"');
          
          if (!html.includes('name="viewport"')) {
            const viewportMeta = '<meta name="viewport" content="width=device-width, initial-scale=1.0" />';
            html = html.replace('<head>', `<head>\n    ${viewportMeta}`);
          }
        }
        
        return html;
      }
    }
  };
}
